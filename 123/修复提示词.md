-### **套利流程**: 达到期货溢价（+）阈值开仓 →锁定差价-> 等待趋同 → 现货溢价（-）达到阈值-> 平仓
+ 代表期货溢价   -代表现货溢价 不代表负数！
- **一切以### **（通用系统支持任意代币的角度来）**深度审查修复！  

重点任务：
2025-08-01 14:53:55.455 [ERROR] [core.trading_system_initializer] ❌ WebSocket启动条件未满足: ['网络连接']
2025-08-01 14:53:55.455 [ERROR] [main] ❌ 统一初始化模块失败
审查错误原因！
第一：精准定位问题： 
1. 查看docs ！ 尤其是 07文档！ 多看文档确保足够了解！ 禁止造轮子，严格按照要求来实现！
2. 深度检查实际代码！进行手动审查 ！
3. 手动审查完毕后，创建精确的诊断脚本，精准定位错误！ 模拟失败场景，进行精准修复！ 确保诊断脚本的质量！确保修复方向正确！
 
第二：深度思考问题： 
## 📋 每次检查完毕后，你必须回答的问题（内部检查清单） 
1. 现有架构中是否已有此功能？（引用【系统核心知识库】中的模块列表回答） 
2. 是否应该在统一模块中实现？（统一模块） 
3. 问题的根本原因是什么？（基于【精准定位问题】的分析） 
4. 检查链路和接口的结果是什么？（基于【精准定位问题】的分析） 
5. 其他两个交易所是否有同样问题？（基于【精准定位问题】的分析） 
6. 如何从源头最优解决问题？（基于【统一修复】的原则） 
7. 是否重复调用，存在造轮子！（进行对比，优质整合删除。） 
8. 横向深度全面查阅资料并思考！ 确保万无一失！ （包括doc 中的md文档，和 官方SDK在项目内） 永远不要忘记，这是个通用多代币期货溢价套利！ 
 
第三：优化规则和要求 
🔗 链路完整性与一致性优化： 
1. 所有接口参数、入参顺序、命名风格必须保持统一； 
2. 严格避免链路中断（如：调用丢失、上下游类型不匹配、数据未透传）； 
3. 自动合并冗余的调用路径或重复链路，提高结构简洁性与稳定性； 
4. 进行优化后！自动更新doc 中的md文件 修复记录更新到123\docs\07B_核心问题修复专项文档.md  功能新增更新到123\docs\07_全流程工作流文档.md 保持07_全流程工作流的权威性和整洁。 
5. 禁止使用修复脚本进行修复，必须手动修复！
6. ## 100确%确定?修复优化没有造车轮?? 使用了统一模块？没有引入新的问题？ 完美修复？ 确保功能实现？？ 职责清晰，没有重复，没有冗余，没有接口不统一 接口不兼容！链路错误！并且测试非常权威没有问题？ 
7.  禁止任何模拟数据！
  
