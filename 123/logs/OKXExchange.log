2025-08-01 10:10:25 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-01 10:10:25 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-01 10:10:25 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-01 10:10:25 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-01 10:10:25.228 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-01 10:10:25.228 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-01 10:10:25 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-01 10:10:25 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-01 10:10:25 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-01 10:10:25 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-01 10:10:25.683 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:10:26.012 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:26.012 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:26.357 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:10:26.357 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:10:26 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-01 10:10:26.358 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:10:26.682 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:26.682 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:27.001 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:10:27.001 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:10:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-01 10:10:27.001 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:10:27.341 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:27.341 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:27.767 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:10:27.767 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:10:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-01 10:10:27.768 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:10:28.018 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:28.019 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:28.328 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:10:28.328 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:10:28 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-01 10:10:28 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-01 10:10:28.643 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 10:10:30.030 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 10:10:31.517 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:10:32.170 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:10:32.749 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:10:33.073 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:10:33.641 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:10:34.212 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:10:54.093 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:54.093 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:54.766 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:54.766 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:55.426 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:55.427 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:56.077 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:56.077 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:56.747 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:56.747 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:10:57.405 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:10:57.405 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:39.600 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 10:11:39.931 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 10:11:39.949 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:11:39.960 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:11:39.963 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-01 10:11:42.376 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-01 10:11:42.697 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-01 10:11:42.702 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-01 10:11:42.716 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-01 10:11:42.807 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-01 10:11:43.809 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.141 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.142 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.142 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.142 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.142 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.142 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.143 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 10:11:44.222 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:44.223 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:44.553 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:44.553 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:44.557 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:44.557 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:44.564 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:44.564 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.572 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.572 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 10:11:44.589 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.589 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.591 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 10:11:44.596 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:44.597 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.602 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.602 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 10:11:44.604 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:44.604 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 10:11:44.884 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:44.884 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:44.890 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:44.890 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:44.891 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:44.891 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:44.893 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:44.893 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:46.722 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:46.722 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:46.976 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:46.976 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:46.979 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:46.979 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:46.984 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:46.984 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:46.985 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:46.985 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:46.985 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-01 10:11:46.985 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 10:11:47.309 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:47.310 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:47.310 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:47.310 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:47.314 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:47.314 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:47.316 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 10:11:47.316 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 10:11:58.069 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 10:11:59.570 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 10:12:01.071 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:01.756 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:02.327 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:02.679 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:12:03.255 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:12:03.828 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:12:08.544 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 10:12:10.068 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 10:12:11.621 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:12.225 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:12.812 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 10:12:13.155 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:12:13.759 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 10:12:14.332 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
