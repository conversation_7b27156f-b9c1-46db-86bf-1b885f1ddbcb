2025-08-01 10:10:32.749 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:10:34.213 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.571 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.572 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.572 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44.589 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.589 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.590 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.591 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.599 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.600 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.601 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.602 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.602 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 10:11:44.608 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 10:11:44.609 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 10:11:45.721 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 10:11:45.721 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 10:11:45.721 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-01 10:12:02.328 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:12:03.829 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:12:12.812 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:12:14.333 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-01 10:12:57.489 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:12:58.078 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:13:08.803 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:13:13.255 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:13:27.516 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:14:11.236 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:14:17.651 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:14:41.868 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:14:43.804 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
2025-08-01 10:14:47.864 [ERROR] [core.data_snapshot_validator.DataSnapshotValidator] 创建快照异常: cannot access local variable 'ensure_milliseconds_timestamp' where it is not associated with a value
