#!/usr/bin/env python3
"""
网络连接诊断脚本 - 精准定位WebSocket启动条件未满足问题
2025-08-01 创建
"""

import asyncio
import time
import json
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class NetworkConnectionDiagnostic:
    """网络连接诊断器"""

    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "test_results": {},
            "summary": {},
            "recommendations": []
        }

    async def run_comprehensive_diagnosis(self):
        """运行全面网络连接诊断"""
        print("🔍 开始网络连接诊断...")

        # 1. 基础网络连接测试
        await self._test_basic_network_connectivity()

        # 2. 交易所API连接测试
        await self._test_exchange_api_connectivity()

        # 3. WebSocket连接测试
        await self._test_websocket_connectivity()

        # 4. DNS解析测试
        await self._test_dns_resolution()

        # 5. 系统依赖检查
        await self._test_system_dependencies()

        # 6. 生成诊断报告
        self._generate_diagnosis_report()

        return self.results
    
    async def _test_basic_network_connectivity(self):
        """测试基础网络连接"""
        print("📡 测试基础网络连接...")

        # 首先检查aiohttp是否可用
        try:
            import aiohttp
        except ImportError:
            print("  ❌ aiohttp库未安装，跳过HTTP连接测试")
            self.results["test_results"]["basic_connectivity"] = {
                "error": "aiohttp库未安装"
            }
            return

        test_urls = [
            "https://httpbin.org/status/200",
            "https://www.google.com",
            "https://api.github.com",
            "https://httpbin.org/get"
        ]

        connectivity_results = {}

        for url in test_urls:
            try:
                start_time = time.time()
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=10) as response:
                        end_time = time.time()
                        latency = (end_time - start_time) * 1000

                        connectivity_results[url] = {
                            "status": "success",
                            "http_status": response.status,
                            "latency_ms": round(latency, 2),
                            "response_size": len(await response.text())
                        }
                        print(f"  ✅ {url}: {response.status} ({latency:.1f}ms)")

            except Exception as e:
                connectivity_results[url] = {
                    "status": "failed",
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"  ❌ {url}: {e}")

        self.results["test_results"]["basic_connectivity"] = connectivity_results
    
    async def _test_exchange_api_connectivity(self):
        """测试交易所API连接"""
        print("🏦 测试交易所API连接...")

        # 检查aiohttp是否可用
        try:
            import aiohttp
        except ImportError:
            print("  ❌ aiohttp库未安装，跳过交易所API连接测试")
            self.results["test_results"]["exchange_api"] = {
                "error": "aiohttp库未安装"
            }
            return

        exchange_endpoints = {
            "gate": "https://api.gateio.ws/api/v4/spot/time",
            "bybit": "https://api.bybit.com/v5/market/time",
            "okx": "https://www.okx.com/api/v5/public/time"
        }

        api_results = {}

        for exchange, endpoint in exchange_endpoints.items():
            try:
                start_time = time.time()
                async with aiohttp.ClientSession() as session:
                    async with session.get(endpoint, timeout=10) as response:
                        end_time = time.time()
                        latency = (end_time - start_time) * 1000

                        response_data = await response.json()

                        api_results[exchange] = {
                            "status": "success",
                            "http_status": response.status,
                            "latency_ms": round(latency, 2),
                            "response_data": response_data
                        }
                        print(f"  ✅ {exchange}: {response.status} ({latency:.1f}ms)")

            except Exception as e:
                api_results[exchange] = {
                    "status": "failed",
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"  ❌ {exchange}: {e}")

        self.results["test_results"]["exchange_api"] = api_results
    
    async def _test_websocket_connectivity(self):
        """测试WebSocket连接"""
        print("🔌 测试WebSocket连接...")
        
        websocket_endpoints = {
            "gate_spot": "wss://api.gateio.ws/ws/v4/",
            "bybit_spot": "wss://stream.bybit.com/v5/public/spot",
            "okx_spot": "wss://ws.okx.com:8443/ws/v5/public"
        }
        
        ws_results = {}
        
        for name, endpoint in websocket_endpoints.items():
            try:
                import websockets
                
                start_time = time.time()
                async with websockets.connect(endpoint, timeout=10) as websocket:
                    end_time = time.time()
                    latency = (end_time - start_time) * 1000
                    
                    ws_results[name] = {
                        "status": "success",
                        "latency_ms": round(latency, 2),
                        "endpoint": endpoint
                    }
                    print(f"  ✅ {name}: 连接成功 ({latency:.1f}ms)")
                    
            except ImportError:
                ws_results[name] = {
                    "status": "failed",
                    "error": "websockets库未安装",
                    "error_type": "ImportError"
                }
                print(f"  ⚠️ {name}: websockets库未安装")
                
            except Exception as e:
                ws_results[name] = {
                    "status": "failed",
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"  ❌ {name}: {e}")
        
        self.results["test_results"]["websocket"] = ws_results
    
    async def _test_dns_resolution(self):
        """测试DNS解析"""
        print("🌐 测试DNS解析...")
        
        domains = [
            "api.gateio.ws",
            "api.bybit.com", 
            "www.okx.com",
            "httpbin.org",
            "google.com"
        ]
        
        dns_results = {}
        
        for domain in domains:
            try:
                import socket
                start_time = time.time()
                ip_address = socket.gethostbyname(domain)
                end_time = time.time()
                latency = (end_time - start_time) * 1000
                
                dns_results[domain] = {
                    "status": "success",
                    "ip_address": ip_address,
                    "latency_ms": round(latency, 2)
                }
                print(f"  ✅ {domain}: {ip_address} ({latency:.1f}ms)")
                
            except Exception as e:
                dns_results[domain] = {
                    "status": "failed",
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"  ❌ {domain}: {e}")
        
        self.results["test_results"]["dns_resolution"] = dns_results
    
    async def _test_system_dependencies(self):
        """测试系统依赖"""
        print("📦 测试系统依赖...")
        
        dependencies = [
            "aiohttp",
            "websockets", 
            "asyncio",
            "json",
            "time",
            "socket"
        ]
        
        dep_results = {}
        
        for dep in dependencies:
            try:
                __import__(dep)
                dep_results[dep] = {
                    "status": "available",
                    "version": getattr(__import__(dep), "__version__", "unknown")
                }
                print(f"  ✅ {dep}: 可用")
                
            except ImportError as e:
                dep_results[dep] = {
                    "status": "missing",
                    "error": str(e)
                }
                print(f"  ❌ {dep}: 缺失")
        
        self.results["test_results"]["dependencies"] = dep_results
    
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n📊 生成诊断报告...")
        
        # 统计成功率
        total_tests = 0
        successful_tests = 0
        
        for category, tests in self.results["test_results"].items():
            for test_name, result in tests.items():
                total_tests += 1
                if result.get("status") in ["success", "available"]:
                    successful_tests += 1
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.results["summary"] = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": round(success_rate, 1),
            "overall_status": "healthy" if success_rate >= 80 else "problematic"
        }
        
        # 生成建议
        if success_rate < 80:
            self.results["recommendations"].append("网络连接存在问题，建议检查网络配置")
        
        if not self.results["test_results"]["dependencies"].get("websockets", {}).get("status") == "available":
            self.results["recommendations"].append("建议安装websockets库: pip install websockets")
        
        if not self.results["test_results"]["dependencies"].get("aiohttp", {}).get("status") == "available":
            self.results["recommendations"].append("建议安装aiohttp库: pip install aiohttp")
        
        print(f"📈 总体成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"🎯 系统状态: {self.results['summary']['overall_status']}")

async def main():
    """主函数"""
    diagnostic = NetworkConnectionDiagnostic()
    results = await diagnostic.run_comprehensive_diagnosis()
    
    # 保存诊断结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"network_diagnosis_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 诊断结果已保存到: {filename}")
    
    # 输出关键信息
    print("\n🔍 关键发现:")
    for category, tests in results["test_results"].items():
        failed_tests = [name for name, result in tests.items() 
                       if result.get("status") not in ["success", "available"]]
        if failed_tests:
            print(f"  ❌ {category}: {', '.join(failed_tests)}")
    
    if results["recommendations"]:
        print("\n💡 建议:")
        for rec in results["recommendations"]:
            print(f"  • {rec}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
