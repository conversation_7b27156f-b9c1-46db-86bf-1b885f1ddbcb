"""
OKX WebSocket客户端
支持现货和期货的深度数据
"""

import asyncio
import json
import time
import logging
import os
import sys
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

# 尝试导入基类和自定义日志系统
try:
    from websocket.ws_client import WebSocketClient
    # 确保使用正确的日志器
    logger = logging.getLogger("websocket.okx")
except ImportError:
    # 开发环境导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from websocket.ws_client import WebSocketClient
    # 确保使用正确的日志器
    logger = logging.getLogger("websocket.okx")


class OKXWebSocketClient(WebSocketClient):
    """OKX WebSocket客户端"""
    
    def __init__(self, market_type: str = "spot", settings=None):
        """
        初始化OKX WebSocket客户端
        Args:
            market_type: 市场类型，可选值: "spot", "futures"
            settings: 全局配置，可选
        """
        super().__init__("OKX", settings)  # 修复：与其他WebSocket客户端保持一致
        self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        self.market_type = market_type.lower()
        self.symbols = []

        # 🔥 时间同步机制
        self.time_offset = 0  # 时间偏移（秒）
        self.time_synced = False
        self.last_sync_time = 0

        if self.market_type == "spot":
            self._log_info("初始化OKX现货WebSocket")
        elif self.market_type == "futures" or self.market_type == "swap":
            self._log_info("初始化OKX合约WebSocket")
        else:
            self._log_error(f"不支持的市场类型: {market_type}")
            raise ValueError(f"不支持的市场类型: {market_type}")

        # 记录订阅频道
        self.active_channels = set()        
        # 🔥 新增：订单簿状态维护 - 解决增量更新问题
        self.orderbook_states = {}  # 维护每个交易对的完整订单簿状态
        self.orderbook_locks = {}   # 防止并发更新冲突
    
    def set_symbols(self, symbols: List[str]):
        """
        设置交易对
        
        Args:
            symbols: 交易对列表，例如 ["BTC-USDT", "ETH-USDT"]
        """
        formatted_symbols = []
        for symbol in symbols:
            # 统一格式，OKX使用'-'分隔
            s = symbol.replace("_", "-").upper()
            formatted_symbols.append(s)
        
        self.symbols = formatted_symbols
        self._log_info(f"设置交易对: {', '.join(formatted_symbols)}")

    async def _sync_time(self):
        """🔥 已删除：使用统一时间戳处理器替代"""
        # 这个方法已被统一时间戳处理器替代，保留空实现以兼容
        pass



    async def run(self):
        """运行WebSocket客户端 - 🔥 修复：移除独立时间同步，使用集中式同步"""
        # 🔥 关键修复：移除WebSocket客户端的独立时间同步调用
        # 时间同步现在由系统启动时的集中式同步管理，避免并发冲突

        from websocket.unified_timestamp_processor import get_timestamp_processor
        processor = get_timestamp_processor("okx")

        # 仅检查同步状态，不执行同步操作
        if processor.time_synced:
            self._log_info(f"✅ OKX时间已同步，偏移量: {processor.time_offset}ms")
        else:
            self._log_warning(f"⚠️ OKX时间未同步，将使用统一时间基准")

        # 调用父类的run方法
        await super().run()

    def get_ws_url(self):
        """获取WebSocket URL"""
        return self.ws_url
    
    async def subscribe_channels(self):
        """订阅频道"""
        if not self.symbols:
            self._log_error("未设置交易对，无法订阅")
            return False
        
        try:
            # 构建订阅参数
            args = []
            
            for symbol in self.symbols:
                # OKX的instType: SPOT(现货), SWAP(永续合约)
                inst_type = "SPOT" if self.market_type == "spot" else "SWAP"
                
                # ticker订阅已移除
                
                # 🔥 根据官方SDK修复：使用正确的books频道
                # OKX官方SDK显示正确频道名为"books"，不是"books50"
                args.append({
                    "channel": "books",  # 🔥 官方SDK标准频道名
                    "instId": symbol
                })
                self.active_channels.add(f"books:{symbol}")
                
                # 🚀 优化：删除不必要的成交数据订阅
                # args.append({
                #     "channel": "trades",
                #     "instId": symbol
                # })
                # self.active_channels.add(f"trades:{symbol}")  # 套利系统不需要成交数据
            
            # 🚀 官方SDK要求：优化并行分批订阅策略
            # 参考官方SDK: okx-sdk-master/README.md multiple()示例

            subscription_success = True
            batch_size = 10  # 🚀 提升批次大小，减少网络往返

            # 🚀 创建并行任务列表
            subscription_tasks = []

            for i in range(0, len(args), batch_size):
                batch = args[i:i+batch_size]
                # 发送订阅消息
                sub_msg = {
                    "op": "subscribe",
                    "args": batch
                }

                # 创建异步任务
                task = self._send_subscription_batch(sub_msg, i//batch_size+1)
                subscription_tasks.append(task)

            # 🚀 并行执行所有订阅任务
            if subscription_tasks:
                results = await asyncio.gather(*subscription_tasks, return_exceptions=True)

                # 检查结果
                success_count = 0
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        self._log_error(f"订阅批次{i+1}失败: {result}")
                        subscription_success = False
                    elif result:
                        success_count += 1
                    else:
                        subscription_success = False

                self._log_info(f"并行订阅完成: {success_count}/{len(results)} 个批次成功")
            
            return subscription_success
        except Exception as e:
            self._log_error(f"订阅频道异常: {e}", exc_info=True)
            return False

    async def _send_subscription_batch(self, sub_msg: dict, batch_num: int) -> bool:
        """发送单个订阅批次 - 🚀 并行订阅优化"""
        try:
            self._log_debug(f"发送订阅请求 (批次 {batch_num}): {json.dumps(sub_msg)[:200]}...")
            success = await self.send(sub_msg)

            if success:
                self._log_info(f"订阅频道请求发送成功: 批次{batch_num}")
                return True
            else:
                self._log_error(f"订阅频道请求发送失败: 批次{batch_num}")

                # 🔥 新增：记录订阅失败日志
                from .websocket_logger import log_websocket_subscription_failure
                log_websocket_subscription_failure("error", f"订阅批次发送失败",
                                                 exchange="okx",
                                                 market_type=self.market_type,
                                                 batch_number=batch_num)
                return False

        except Exception as e:
            self._log_error(f"发送订阅批次{batch_num}异常: {e}")
            return False
    
    async def handle_message(self, data: dict):
        """处理接收到的消息 - 🔥 智能错误处理：自动适应不可用交易对"""
        try:
            if "event" in data:
                if data["event"] == "subscribe":
                    # 静默处理订阅确认
                    pass
                elif data["event"] == "error":
                    # 🔥 智能错误处理：区分错误类型
                    error_code = data.get('code', '')
                    error_msg = data.get('msg', '')
                    
                    if error_code == '60018' and "doesn't exist" in error_msg:
                        # 🚀 交易对不存在 - 这是正常的，自动跳过
                        self._log_debug(f"交易对不存在，已自动跳过: {error_msg}")
                        # 不记录为错误，这是智能适应的一部分
                    else:
                        # 🔥 其他错误才记录
                        self._log_error(f"WebSocket错误 [{error_code}]: {error_msg}")

                        # 🔥 新增：记录订阅失败日志
                        from .websocket_logger import log_websocket_subscription_failure
                        log_websocket_subscription_failure("error", f"WebSocket订阅错误",
                                                         exchange="okx",
                                                         market_type=self.market_type,
                                                         error_code=error_code,
                                                         error_message=error_msg)
                return
                
            if "data" not in data:
                return
                
            # 🔥 修复：从arg中获取instId和channel
            arg = data.get("arg", {})
            inst_id = arg.get("instId", "")
            channel = arg.get("channel", "")

            # 🔥 修复：检查inst_id是否为空
            if not inst_id or inst_id.strip() == "":
                return

            # 处理数据，不记录任何统计
            for item in data["data"]:
                # 静默处理各类数据
                if "tickers" in channel:
                    # ticker处理已移除，跳过
                    self._log_debug(f"跳过ticker数据: {inst_id}")
                elif "books" in channel:  # 🔥 修复：处理50档深度数据（包含前10档）
                    await self._handle_orderbook(inst_id, item)
                elif "trades" in channel:
                    await self._handle_trades(inst_id, item)
                    
        except Exception as e:
            self._log_error(f"处理消息异常: {e}")
    
    # 🔥 TICKER处理方法已完全删除 - 系统只使用OrderBook数据

    async def _handle_orderbook(self, symbol: str, book: Dict[str, Any]):
        """处理订单簿数据 - 🔥 修复版本：维护完整订单簿状态"""
        try:
            import asyncio
            import time
            # 🔥 新增：使用统一订单簿验证器和性能监控
            from websocket.orderbook_validator import get_orderbook_validator
            from websocket.performance_monitor import record_message_latency

            start_time = time.time()

            # 🔥 关键修复：维护完整的订单簿状态
            if symbol not in self.orderbook_states:
                self.orderbook_states[symbol] = {
                    "asks": {},  # {price: quantity}
                    "bids": {},  # {price: quantity}
                    "last_update": 0
                }

            if symbol not in self.orderbook_locks:
                self.orderbook_locks[symbol] = asyncio.Lock()

            # 使用锁防止并发更新
            async with self.orderbook_locks[symbol]:
                state = self.orderbook_states[symbol]

                # 提取增量数据
                asks = book.get("asks", [])  # [[价格, 数量, 未使用, 订单数]]
                bids = book.get("bids", [])  # [[价格, 数量, 未使用, 订单数]]
                
                # 🔥 处理增量更新
                # 更新asks - 🔥 使用高精度Decimal处理
                for ask in asks:
                    if len(ask) >= 2:
                        from decimal import Decimal
                        price = Decimal(str(ask[0]))
                        quantity = Decimal(str(ask[1]))
                        # 🔥 标准验证：价格必须大于0
                        if price > 0:
                            if quantity == 0:
                                # 数量为0表示删除该价格档位
                                state["asks"].pop(price, None)
                            else:
                                # 更新或添加价格档位 - 🔥 使用Decimal保持精度
                                state["asks"][price] = quantity

                # 更新bids - 🔥 使用高精度Decimal处理
                for bid in bids:
                    if len(bid) >= 2:
                        from decimal import Decimal
                        price = Decimal(str(bid[0]))
                        quantity = Decimal(str(bid[1]))
                        # 🔥 标准验证：价格必须大于0
                        if price > 0:
                            if quantity == 0:
                                # 数量为0表示删除该价格档位
                                state["bids"].pop(price, None)
                            else:
                                # 更新或添加价格档位 - 🔥 使用Decimal保持精度
                                state["bids"][price] = quantity
                
                # 🔥 修复：生成完整的前30档订单簿
                # 排序asks (价格从低到高)
                sorted_asks = sorted(state["asks"].items())[:30]  # 🔥 修复：升级为30档深度
                # 排序bids (价格从高到低)
                sorted_bids = sorted(state["bids"].items(), reverse=True)[:30]  # 🔥 修复：升级为30档深度
                
                # 转换为标准格式
                formatted_asks = [[price, quantity] for price, quantity in sorted_asks]
                formatted_bids = [[price, quantity] for price, quantity in sorted_bids]
                
                # 🔥 修复：放宽深度要求，只要有数据就处理（与其他交易所保持一致）
                if len(formatted_asks) == 0 and len(formatted_bids) == 0:
                    self._log_debug(f"⚠️ OKX订单簿无数据: {symbol} - asks={len(formatted_asks)}, bids={len(formatted_bids)}")
                    return

                # 🔥 新增：使用统一订单簿验证器
                validator = get_orderbook_validator()
                orderbook_data = {
                    'asks': formatted_asks,
                    'bids': formatted_bids
                }
                validation_result = validator.validate_orderbook_data(
                    orderbook_data,
                    exchange="okx",
                    symbol=symbol,
                    market_type=self.market_type
                )

                if not validation_result.is_valid:
                    self._log_warning(f"⚠️ OKX订单簿验证失败: {validation_result.error_message}")
                    return

                # 🔥 关键修复：使用统一时间戳处理器，确保时间同步
                from websocket.unified_timestamp_processor import get_synced_timestamp
                timestamp = int(book.get("ts", get_synced_timestamp("okx", book)))

                # 标准化交易对名称
                from exchanges.currency_adapter import normalize_symbol
                standard_symbol = normalize_symbol(symbol)

                # 🔥 使用统一格式化器创建订单簿数据
                from websocket.unified_data_formatter import get_orderbook_formatter

                formatter = get_orderbook_formatter()
                orderbook_data = formatter.format_orderbook_data(
                    asks=formatted_asks,
                    bids=formatted_bids,
                    symbol=standard_symbol,
                    exchange="okx",
                    market_type=self.market_type,
                    timestamp=timestamp
                )

                # 🔥 记录性能指标
                record_message_latency(start_time)

                self._log_debug(f"✅ OKX完整订单簿: {symbol} - asks={len(formatted_asks)}, bids={len(formatted_bids)}")

                # 🔥 发送完整的订单簿数据
                self.emit("market_data", orderbook_data)
                
        except Exception as e:
            self._log_error(f"🚨 OKX订单簿处理失败: {symbol} - {str(e)}")
            raise
    async def _handle_trades(self, symbol: str, trade_data: Dict[str, Any]):
        """
        处理成交数据
        
        Args:
            symbol: 交易对
            trade_data: 单个成交数据对象
        """
        try:
            # 提取成交数据 - 🔥 使用高精度Decimal处理
            from decimal import Decimal
            price = Decimal(str(trade_data.get("px", 0)))
            amount = Decimal(str(trade_data.get("sz", 0)))

            # 🔥 关键修复：使用统一时间戳处理器，确保时间同步
            from websocket.unified_timestamp_processor import get_synced_timestamp
            timestamp = int(trade_data.get("ts", get_synced_timestamp("okx", trade_data)))
            side = trade_data.get("side", "").lower()  # "buy" or "sell"

            trade = {
                "price": float(price),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                "amount": float(amount),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                "timestamp": timestamp,
                "side": side
            }
            
            # 创建标准化的成交数据
            trade_result = {
                "exchange": "okx",
                "symbol": symbol,
                "timestamp": timestamp,
                "trades": [trade],  # 单笔成交包装为列表
                "market_type": self.market_type
            }
            
            # 记录debug日志，只写入日志文件
            self._log_debug(f"成交 {symbol}: price={price} amount={amount} side={side}")
            
            # 触发回调
            self.emit("trade", trade_result)
            
        except Exception as e:
            self._log_error(f"处理成交数据出错: {str(e)}", exc_info=True)
    
    async def send_heartbeat(self):
        """发送心跳包"""
        ping_msg = "ping"
        if await self.send(ping_msg):
            self._log_debug("发送心跳: ping")
            self.last_message_time = time.time()  # 更新最后消息时间
            return True
        return False
    
    async def send(self, message):
        """
        发送WebSocket消息
        
        Args:
            message: 要发送的消息对象
            
        Returns:
            bool: 发送是否成功
        """
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket未连接，无法发送消息")
            return False
            
        try:
            # 如果消息是字典或列表，则转换为JSON字符串
            if isinstance(message, (dict, list)):
                message_str = json.dumps(message)
            else:
                message_str = message
                
            await self.ws.send(message_str)
            self._log_debug(f"发送消息: {message_str[:100]}...")
            return True
        except Exception as e:
            self._log_error(f"发送消息失败: {str(e)}", exc_info=True)
            return False

    async def close(self):
        """关闭WebSocket客户端"""
        self.running = False
        if self.ws:
            await self.ws.close()
            self.ws = None
        self._log_info(f"WebSocket客户端关闭中...")


async def test_okx_websocket():
    """测试OKX WebSocket连接"""
    # 创建OKX WebSocket客户端
    client = OKXWebSocketClient("OKX", "spot")
    
    # 设置交易对
    client.set_symbols(["BTC-USDT", "ETH-USDT"])
    
    # 注册回调函数
    async def on_orderbook(data):
        # 处理订单簿数据
        pass

    # ticker处理已完全移除

    client.register_callback("orderbook", on_orderbook)
    
    # 启动客户端
    try:
        await client.run()
    except KeyboardInterrupt:
        pass  # 用户中断，关闭连接
    finally:
        await client.close()


if __name__ == "__main__":
    # 配置简单的日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 运行测试
    asyncio.run(test_okx_websocket())