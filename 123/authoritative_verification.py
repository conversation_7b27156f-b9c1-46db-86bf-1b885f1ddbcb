#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 权威修复验证测试
在正确路径下执行关键功能验证
"""

import sys
import os
import time
import json

def run_authoritative_verification():
    """执行权威修复验证"""
    print("🏛️ 开始权威修复验证测试...")
    
    results = {
        "timestamp": int(time.time()),
        "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tests": []
    }
    
    # 测试1: DataSnapshotValidator核心功能
    try:
        print("\n🔧 测试1: DataSnapshotValidator核心功能")
        
        from core.data_snapshot_validator import DataSnapshotValidator
        validator = DataSnapshotValidator()
        
        # 测试属性
        assert validator.max_snapshot_age_ms == 800, f"max_snapshot_age_ms错误: {validator.max_snapshot_age_ms}"
        assert validator.max_timestamp_diff_ms == 800, f"max_timestamp_diff_ms错误: {validator.max_timestamp_diff_ms}"
        
        # 测试方法存在
        assert hasattr(validator, 'create_validated_snapshot'), "缺少create_validated_snapshot方法"
        assert hasattr(validator, 'validate_market_data_snapshot'), "缺少validate_market_data_snapshot方法"
        
        results["tests"].append({
            "name": "DataSnapshotValidator核心功能",
            "status": "PASS",
            "details": "所有属性和方法验证通过"
        })
        print("   ✅ 通过")
        
    except Exception as e:
        results["tests"].append({
            "name": "DataSnapshotValidator核心功能", 
            "status": "FAIL",
            "error": str(e)
        })
        print(f"   ❌ 失败: {e}")
    
    # 测试2: 时间戳处理函数
    try:
        print("\n🔧 测试2: 时间戳处理函数")
        
        from core.data_snapshot_validator import ensure_milliseconds_timestamp, calculate_data_age
        
        # 边界测试
        test_cases = [
            (None, "当前时间戳"),
            (0, "当前时间戳"),
            (1754051355.665, 1754051355665),
            (1754051355665, 1754051355665)
        ]
        
        for input_val, expected in test_cases:
            result = ensure_milliseconds_timestamp(input_val)
            if expected == "当前时间戳":
                current_ms = int(time.time() * 1000)
                assert abs(result - current_ms) < 1000, f"时间戳不在合理范围: {result}"
            else:
                assert result == expected, f"输入{input_val}期望{expected}实际{result}"
        
        # 测试数据年龄计算
        test_timestamp = int(time.time() * 1000) - 5000  # 5秒前
        age = calculate_data_age(test_timestamp, time.time())
        assert 4.5 <= age <= 5.5, f"数据年龄计算错误: {age}"
        
        results["tests"].append({
            "name": "时间戳处理函数",
            "status": "PASS", 
            "details": f"所有边界测试通过，数据年龄计算准确: {age:.3f}s"
        })
        print("   ✅ 通过")
        
    except Exception as e:
        results["tests"].append({
            "name": "时间戳处理函数",
            "status": "FAIL",
            "error": str(e)
        })
        print(f"   ❌ 失败: {e}")
    
    # 测试3: 快照创建完整流程
    try:
        print("\n🔧 测试3: 快照创建完整流程")
        
        from core.data_snapshot_validator import DataSnapshotValidator
        validator = DataSnapshotValidator()
        
        class MockData:
            def __init__(self, timestamp=None):
                self.timestamp = timestamp or int(time.time() * 1000)
                self.exchange = "test"
        
        # 创建快照
        snapshot = validator.create_validated_snapshot(
            spot_data=MockData(),
            futures_data=MockData(),
            spot_orderbook={'asks': [[100, 1]], 'bids': [[99, 1]]},
            futures_orderbook={'asks': [[101, 1]], 'bids': [[100, 1]]}
        )
        
        assert snapshot is not None, "快照创建失败"
        assert 'snapshot_timestamp' in snapshot, "快照缺少时间戳"
        assert 'spot_data' in snapshot, "快照缺少现货数据"
        assert 'futures_data' in snapshot, "快照缺少期货数据"
        
        # 验证快照
        validation_result = validator.validate_market_data_snapshot(snapshot)
        
        results["tests"].append({
            "name": "快照创建完整流程",
            "status": "PASS",
            "details": f"快照创建成功，验证结果: {validation_result.is_valid}"
        })
        print("   ✅ 通过")
        
    except Exception as e:
        results["tests"].append({
            "name": "快照创建完整流程",
            "status": "FAIL", 
            "error": str(e)
        })
        print(f"   ❌ 失败: {e}")
    
    # 测试4: 统一模块一致性
    try:
        print("\n🔧 测试4: 统一模块一致性")
        
        from websocket.unified_timestamp_processor import ensure_milliseconds_timestamp as ems_unified
        from core.data_snapshot_validator import ensure_milliseconds_timestamp as ems_dsv
        
        test_timestamp = 1754051355.665
        result_unified = ems_unified(test_timestamp)
        result_dsv = ems_dsv(test_timestamp)
        
        assert result_unified == result_dsv, f"函数不一致: 统一模块{result_unified} vs DSV{result_dsv}"
        
        results["tests"].append({
            "name": "统一模块一致性",
            "status": "PASS",
            "details": f"统一模块和DSV结果一致: {result_unified}"
        })
        print("   ✅ 通过")
        
    except Exception as e:
        results["tests"].append({
            "name": "统一模块一致性",
            "status": "FAIL",
            "error": str(e)
        })
        print(f"   ❌ 失败: {e}")
    
    # 测试5: 多交易所时间戳同步
    try:
        print("\n🔧 测试5: 多交易所时间戳同步")
        
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        exchanges = ["gate", "bybit", "okx"]
        timestamps = {}
        
        for exchange in exchanges:
            processor = get_timestamp_processor(exchange)
            timestamp = processor.get_synced_timestamp(None)
            timestamps[exchange] = timestamp
        
        # 计算最大时间差
        max_diff = 0
        for i, ex1 in enumerate(exchanges):
            for ex2 in exchanges[i+1:]:
                diff = abs(timestamps[ex1] - timestamps[ex2])
                max_diff = max(max_diff, diff)
        
        # 目标：1000ms内
        target_achieved = max_diff <= 1000
        
        results["tests"].append({
            "name": "多交易所时间戳同步",
            "status": "PASS" if target_achieved else "FAIL",
            "details": f"最大时间差: {max_diff}ms, 目标(<1000ms): {'达成' if target_achieved else '未达成'}"
        })
        print(f"   {'✅' if target_achieved else '❌'} {'通过' if target_achieved else '失败'}: 最大时间差{max_diff}ms")
        
    except Exception as e:
        results["tests"].append({
            "name": "多交易所时间戳同步",
            "status": "ERROR",
            "error": str(e)
        })
        print(f"   ❌ 错误: {e}")
    
    # 计算总体结果
    total_tests = len(results["tests"])
    passed_tests = len([t for t in results["tests"] if t["status"] == "PASS"])
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 质量评级
    if success_rate >= 95:
        quality_grade = "🏛️ 机构级别"
        quality_level = "INSTITUTIONAL"
    elif success_rate >= 85:
        quality_grade = "🏢 企业级别"
        quality_level = "ENTERPRISE"
    elif success_rate >= 70:
        quality_grade = "📊 商业级别"
        quality_level = "COMMERCIAL"
    else:
        quality_grade = "⚠️ 需要改进"
        quality_level = "NEEDS_IMPROVEMENT"
    
    results["summary"] = {
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "success_rate": success_rate,
        "quality_grade": quality_grade,
        "quality_level": quality_level
    }
    
    print(f"\n📊 权威验证结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    print(f"   质量评级: {quality_grade}")
    
    # 保存结果
    output_file = f"authoritative_verification_{int(time.time())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 验证结果已保存到: {output_file}")
    
    if success_rate >= 80:
        print(f"\n🎉 {quality_grade} 验证通过！修复质量达标。")
        return True
    else:
        print(f"\n⚠️ {quality_grade} 需要进一步改进。")
        return False

if __name__ == "__main__":
    run_authoritative_verification()