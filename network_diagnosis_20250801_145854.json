{"timestamp": 1754053131.3199341, "test_results": {"basic_connectivity": {"https://httpbin.org/status/200": {"status": "success", "http_status": 503, "latency_ms": 616.63, "response_size": 162}, "https://www.google.com": {"status": "success", "http_status": 200, "latency_ms": 586.21, "response_size": 18470}, "https://api.github.com": {"status": "success", "http_status": 200, "latency_ms": 206.38, "response_size": 2262}, "https://httpbin.org/get": {"status": "success", "http_status": 503, "latency_ms": 641.8, "response_size": 162}}, "exchange_api": {"gate": {"status": "success", "http_status": 200, "latency_ms": 20.9, "response_data": {"server_time": 1754053133585}}, "bybit": {"status": "success", "http_status": 200, "latency_ms": 80.94, "response_data": {"retCode": 0, "retMsg": "OK", "result": {"timeSecond": "1754053133", "timeNano": "1754053133633978284"}, "retExtInfo": {}, "time": 1754053133633}}, "okx": {"status": "success", "http_status": 200, "latency_ms": 85.76, "response_data": {"code": "0", "data": [{"ts": "1754053133715"}], "msg": ""}}}, "websocket": {"gate_spot": {"status": "success", "latency_ms": 93.21, "endpoint": "wss://api.gateio.ws/ws/v4/"}, "bybit_spot": {"status": "success", "latency_ms": 275.25, "endpoint": "wss://stream.bybit.com/v5/public/spot"}, "okx_spot": {"status": "success", "latency_ms": 239.17, "endpoint": "wss://ws.okx.com:8443/ws/v5/public"}}, "dns_resolution": {"api.gateio.ws": {"status": "success", "ip_address": "*************", "latency_ms": 1.18}, "api.bybit.com": {"status": "success", "ip_address": "*************", "latency_ms": 0.52}, "www.okx.com": {"status": "success", "ip_address": "*************", "latency_ms": 0.76}, "httpbin.org": {"status": "success", "ip_address": "**************", "latency_ms": 0.49}, "google.com": {"status": "success", "ip_address": "***************", "latency_ms": 0.6}}, "dependencies": {"aiohttp": {"status": "available", "version": "3.12.13"}, "websockets": {"status": "available", "version": "11.0.3"}, "asyncio": {"status": "available", "version": "unknown"}, "json": {"status": "available", "version": "2.0.9"}, "time": {"status": "available", "version": "unknown"}, "socket": {"status": "available", "version": "unknown"}}}, "summary": {"total_tests": 21, "successful_tests": 21, "success_rate": 100.0, "overall_status": "healthy"}, "recommendations": []}